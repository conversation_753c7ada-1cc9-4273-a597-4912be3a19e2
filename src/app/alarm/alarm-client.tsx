"use client";

import { useState, useEffect } from "react";
import { DigitalClock } from "@/components/time/digital-clock";
import { AlarmSettings } from "@/components/alarm/alarm-settings";
import { AlarmIndicator } from "@/components/alarm/alarm-indicator";
import { AlarmNotification } from "@/components/alarm/alarm-notification";
import { useClockSettings } from "@/hooks/useClockSettings";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
  isTemporary?: boolean; // For snooze alarms - not saved to localStorage
  originalId?: string;   // Reference to the original alarm that was snoozed
}

export function AlarmClient() {
  const [alarms, setAlarms] = useState<Alarm[]>([]);
  const [activeAlarm, setActiveAlarm] = useState<Alarm | null>(null);
  const [isAlarmRinging, setIsAlarmRinging] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [alarmQueue, setAlarmQueue] = useState<Alarm[]>([]);
  const [isMounted, setIsMounted] = useState(false);

  // Use shared clock settings from time page
  const { settings: clockSettings, isLoaded } = useClockSettings();

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load alarms from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedAlarms = localStorage.getItem("clockApp_alarms");
        if (savedAlarms) {
          const parsedAlarms = JSON.parse(savedAlarms);
          console.log("Loaded alarms from localStorage:", parsedAlarms);
          setAlarms(parsedAlarms);
        } else {
          console.log("No saved alarms found in localStorage");
        }
      } catch (error) {
        console.error("Error loading alarms from localStorage:", error);
        // Try backup
        try {
          const backupAlarms = localStorage.getItem("clockApp_alarms_backup");
          if (backupAlarms) {
            const parsedBackup = JSON.parse(backupAlarms);
            console.log("Loaded alarms from backup:", parsedBackup);
            setAlarms(parsedBackup);
          }
        } catch (backupError) {
          console.error("Error loading backup alarms:", backupError);
        }
      }
    }
  }, []);

  // Monitor fullscreen state
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullScreenChange);
  }, []);

  // Save alarms to localStorage when they change (only after initial load, exclude temporary alarms)
  useEffect(() => {
    if (isLoaded && typeof window !== 'undefined') {
      try {
        // Only save permanent alarms (exclude temporary snooze alarms)
        const permanentAlarms = alarms.filter(alarm => !alarm.isTemporary);
        const alarmsString = JSON.stringify(permanentAlarms);
        localStorage.setItem("clockApp_alarms", alarmsString);
        console.log("Permanent alarms saved to localStorage:", alarmsString);
        console.log("Temporary alarms (not saved):", alarms.filter(alarm => alarm.isTemporary));

        // Also save to a backup key
        localStorage.setItem("clockApp_alarms_backup", alarmsString);
      } catch (error) {
        console.error("Error saving alarms to localStorage:", error);
      }
    }
  }, [alarms, isLoaded]);

  // Check for alarms that need to ring
  useEffect(() => {
    const checkAlarms = () => {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, "0")}:${now.getMinutes().toString().padStart(2, "0")}`;

      // Find all alarms that should ring at this time
      const alarmsToRing = alarms.filter(
        (alarm) => alarm.enabled && alarm.time === currentTime && now.getSeconds() === 0
      );

      if (alarmsToRing.length > 0 && !isAlarmRinging) {
        // If multiple alarms, add them to queue and ring the first one
        if (alarmsToRing.length > 1) {
          setAlarmQueue(alarmsToRing.slice(1)); // Add remaining alarms to queue
          console.log(`Multiple alarms at ${currentTime}:`, alarmsToRing.map(a => a.title));
        }

        const firstAlarm = alarmsToRing[0];
        setActiveAlarm(firstAlarm);
        setIsAlarmRinging(true);
        console.log(`Alarm triggered: ${firstAlarm.title} at ${currentTime}`);

        // If this is a temporary alarm (snoozed), remove it after triggering
        if (firstAlarm.isTemporary) {
          console.log(`Removing temporary alarm after triggering: ${firstAlarm.title}`);
          setAlarms(prevAlarms => prevAlarms.filter(alarm => alarm.id !== firstAlarm.id));
        }
      }
    };

    const interval = setInterval(checkAlarms, 1000);
    return () => clearInterval(interval);
  }, [alarms, isAlarmRinging]);

  const handleAddAlarm = (alarm: Alarm): boolean => {
    setAlarms((prevAlarms) => [...prevAlarms, alarm]);
    return true; // Return success
  };

  const handleTestAlarm = (alarm: Omit<Alarm, 'id' | 'enabled'>) => {
    // Create a temporary alarm for testing
    const testAlarm = {
      ...alarm,
      id: 'test',
      enabled: true,
    };
    setActiveAlarm(testAlarm);
    setIsAlarmRinging(true);
  };

  const handleToggleAlarm = (id: string) => {
    setAlarms((prevAlarms) =>
      prevAlarms.map((alarm) =>
        alarm.id === id ? { ...alarm, enabled: !alarm.enabled } : alarm
      )
    );
  };

  const handleDeleteAlarm = (id: string) => {
    setAlarms((prevAlarms) => prevAlarms.filter((alarm) => alarm.id !== id));
  };

  const handleCloseAlarm = () => {
    const currentAlarm = activeAlarm;
    setIsAlarmRinging(false);
    setActiveAlarm(null);

    // If the dismissed alarm was temporary, remove it from the list
    if (currentAlarm?.isTemporary) {
      console.log(`Removing dismissed temporary alarm: ${currentAlarm.title}`);
      setAlarms(prevAlarms => prevAlarms.filter(alarm => alarm.id !== currentAlarm.id));
    }

    // Check if there are more alarms in the queue
    if (alarmQueue.length > 0) {
      // Ring the next alarm after a short delay
      setTimeout(() => {
        const nextAlarm = alarmQueue[0];
        setAlarmQueue(prev => prev.slice(1)); // Remove from queue
        setActiveAlarm(nextAlarm);
        setIsAlarmRinging(true);
        console.log(`Next alarm from queue: ${nextAlarm.title}`);

        // If the next alarm is also temporary, remove it from the main list
        if (nextAlarm.isTemporary) {
          setAlarms(prevAlarms => prevAlarms.filter(alarm => alarm.id !== nextAlarm.id));
        }
      }, 1000); // 1 second delay between alarms
    }
  };

  const handleSnoozeAlarm = (minutes: number) => {
    if (!activeAlarm) return;

    const now = new Date();
    const snoozeTime = new Date(now.getTime() + minutes * 60000);
    const newTime = `${snoozeTime.getHours().toString().padStart(2, "0")}:${snoozeTime.getMinutes().toString().padStart(2, "0")}`;

    // Create a temporary snoozed alarm
    const snoozedAlarm: Alarm = {
      id: `${activeAlarm.id}_snooze_${Date.now()}`,
      time: newTime,
      title: `${activeAlarm.title} (Snoozed)`,
      sound: activeAlarm.sound,
      enabled: true,
      isTemporary: true, // Mark as temporary so it's not saved to localStorage
      originalId: activeAlarm.originalId || activeAlarm.id, // Track the original alarm
    };

    // Add the temporary snoozed alarm
    setAlarms((prevAlarms) => [...prevAlarms, snoozedAlarm]);
    setActiveAlarm(null);

    console.log(`Temporary snoozed alarm created: ${snoozedAlarm.title} at ${newTime}`);

    // Check if there are more alarms in the queue
    if (alarmQueue.length > 0) {
      // Ring the next alarm after a short delay
      setTimeout(() => {
        const nextAlarm = alarmQueue[0];
        setAlarmQueue(prev => prev.slice(1)); // Remove from queue
        setActiveAlarm(nextAlarm);
        setIsAlarmRinging(true);
        console.log(`Next alarm from queue after snooze: ${nextAlarm.title}`);
      }, 1000); // 1 second delay between alarms
    } else {
      setIsAlarmRinging(false);
    }
  };

  // Apply background styles to the entire page
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    if (clockSettings.backgroundColor) {
      htmlElement.style.backgroundColor = clockSettings.backgroundColor;
      bodyElement.style.backgroundColor = clockSettings.backgroundColor;
    } else {
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    }

    if (clockSettings.backgroundImage) {
      const imageStyle = `url(${clockSettings.backgroundImage})`;
      htmlElement.style.backgroundImage = imageStyle;
      htmlElement.style.backgroundSize = 'cover';
      htmlElement.style.backgroundPosition = 'center';
      htmlElement.style.backgroundRepeat = 'no-repeat';
      htmlElement.style.backgroundAttachment = 'fixed';

      bodyElement.style.backgroundImage = imageStyle;
      bodyElement.style.backgroundSize = 'cover';
      bodyElement.style.backgroundPosition = 'center';
      bodyElement.style.backgroundRepeat = 'no-repeat';
      bodyElement.style.backgroundAttachment = 'fixed';
    } else {
      htmlElement.style.backgroundImage = '';
      htmlElement.style.backgroundSize = '';
      htmlElement.style.backgroundPosition = '';
      htmlElement.style.backgroundRepeat = '';
      htmlElement.style.backgroundAttachment = '';

      bodyElement.style.backgroundImage = '';
      bodyElement.style.backgroundSize = '';
      bodyElement.style.backgroundPosition = '';
      bodyElement.style.backgroundRepeat = '';
      bodyElement.style.backgroundAttachment = '';
    }

    return () => {
      // Cleanup on unmount
      htmlElement.style.backgroundColor = '';
      htmlElement.style.backgroundImage = '';
      htmlElement.style.backgroundSize = '';
      htmlElement.style.backgroundPosition = '';
      htmlElement.style.backgroundRepeat = '';
      htmlElement.style.backgroundAttachment = '';

      bodyElement.style.backgroundColor = '';
      bodyElement.style.backgroundImage = '';
      bodyElement.style.backgroundSize = '';
      bodyElement.style.backgroundPosition = '';
      bodyElement.style.backgroundRepeat = '';
      bodyElement.style.backgroundAttachment = '';
    };
  }, [clockSettings.backgroundColor, clockSettings.backgroundImage]);

  const mainStyle = {
    backgroundColor: clockSettings.backgroundColor || 'transparent',
    backgroundImage: clockSettings.backgroundImage ? `url(${clockSettings.backgroundImage})` : 'none',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  };

  return (
    <>
      {isFullScreen ? (
        // Full screen layout - settings button in top-right corner
        <>
          <div
            className="w-full h-screen flex items-center justify-center"
            style={{
              ...mainStyle,
              minHeight: '100vh', // Full viewport height
            }}
          >
            {/* Alarm Indicator at the top center */}
            <div className="fixed top-8 left-1/2 transform -translate-x-1/2 z-40">
              <AlarmIndicator
                alarms={alarms}
                onToggleAlarm={handleToggleAlarm}
                onDeleteAlarm={handleDeleteAlarm}
              />
            </div>

            <DigitalClock
              showSeconds={clockSettings.showSeconds}
              showWeekday={clockSettings.showWeekday}
              showDate={clockSettings.showDate}
              showWeekNumber={clockSettings.showWeekNumber}
              use12Hours={clockSettings.use12Hours}
              textColor={clockSettings.textColor}
              fontSize={clockSettings.fontSize}
              fontFamily={clockSettings.fontFamily}
              position={clockSettings.position}
            />
          </div>
        </>
      ) : (
        // Non-fullscreen layout - full width clock with floating settings
        <>
          <div
            className="w-full flex items-center justify-center px-4 py-8 md:py-4"
            style={{
              ...mainStyle,
              minHeight: 'calc(100vh - 120px)', // Account for navigation
            }}
          >
            {/* Alarm Indicator at the top center */}
            <div className="fixed top-32 md:top-24 left-1/2 transform -translate-x-1/2 z-40">
              <AlarmIndicator
                alarms={alarms}
                onToggleAlarm={handleToggleAlarm}
                onDeleteAlarm={handleDeleteAlarm}
              />
            </div>

            <DigitalClock
              showSeconds={clockSettings.showSeconds}
              showWeekday={clockSettings.showWeekday}
              showDate={clockSettings.showDate}
              showWeekNumber={clockSettings.showWeekNumber}
              use12Hours={clockSettings.use12Hours}
              textColor={clockSettings.textColor}
              fontSize={clockSettings.fontSize}
              fontFamily={clockSettings.fontFamily}
              position={clockSettings.position}
            />
          </div>
        </>
      )}

      {/* Alarm Settings panel - mobile: bottom-right, desktop: top-right */}
      <div className={`fixed z-50 ${
        isFullScreen
          ? 'top-4 right-4'
          : 'bottom-4 right-4 md:top-20 md:bottom-auto md:right-4'
      }`}>
        <AlarmSettings
          onAddAlarm={handleAddAlarm}
          onTestAlarm={handleTestAlarm}
          existingAlarms={alarms}
        />
      </div>

      <AlarmNotification
        isOpen={isAlarmRinging}
        onClose={handleCloseAlarm}
        onSnooze={handleSnoozeAlarm}
        alarm={activeAlarm}
      />


    </>
  );
}
