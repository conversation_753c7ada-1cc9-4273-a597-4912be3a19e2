"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { AlarmSEOContent } from "@/components/seo/alarm-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { AlarmClient } from "./alarm-client";

export default function AlarmPage() {
  const [isFullScreen, setIsFullScreen] = useState(false);

  // Monitor fullscreen state
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullScreenChange);
  }, []);

  return (
    <MainLayout>
      <StructuredData type="alarm" />

      {/* Full-height alarm section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)', overflow: 'hidden' }}>
        <AlarmClient />
      </section>

      {/* SEO content section - below the fold, hidden in fullscreen */}
      {!isFullScreen && (
        <section className="relative z-9">
          <AlarmSEOContent />
        </section>
      )}
    </MainLayout>
  );
}
