import { MainLayout } from "@/components/layout/main-layout";
import { AlarmSEOContent } from "@/components/seo/alarm-seo-content";
import { StructuredData } from "@/components/seo/structured-data";
import { AlarmClient } from "./alarm-client";

export default function AlarmPage() {
  return (
    <MainLayout>
      <StructuredData type="alarm" />

      {/* Full-height alarm section */}
      <section className="relative" style={{ height: 'calc(100vh - 4rem)' }}>
        <AlarmClient />
      </section>

      {/* SEO content section - below the fold */}
      <section className="relative z-9">
        <AlarmSEOContent />
      </section>
    </MainLayout>
  );
}
