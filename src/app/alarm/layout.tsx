import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Online Alarm Clock - Free Wake Up Alarms | Best Online Clock",
  description: "Free online alarm clock with multiple alarms, custom sounds, and snooze functionality. Set wake-up alarms, reminders, and notifications. Works on all devices without installation.",
  keywords: "online alarm clock, wake up alarm, free alarm, web alarm, alarm clock online, multiple alarms, snooze alarm, reminder alarm",
  openGraph: {
    title: "Online Alarm Clock - Free Wake Up Alarms | Best Online Clock",
    description: "Free online alarm clock with multiple alarms, custom sounds, and snooze functionality. Set wake-up alarms, reminders, and notifications.",
    url: 'https://bestonlineclock.com/alarm',
    images: [
      {
        url: '/alarm-og-image.png',
        width: 1200,
        height: 630,
        alt: 'Online Alarm Clock Interface',
      },
    ],
  },
  twitter: {
    title: "Online Alarm Clock - Free Wake Up Alarms | Best Online Clock",
    description: "Free online alarm clock with multiple alarms, custom sounds, and snooze functionality. Set wake-up alarms, reminders, and notifications.",
    images: ['/alarm-twitter-image.png'],
  },
  alternates: {
    canonical: '/alarm',
  },
};

export default function AlarmLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
