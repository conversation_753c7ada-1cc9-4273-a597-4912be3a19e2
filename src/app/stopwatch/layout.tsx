import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Online Stopwatch - Precision Timer with Lap Times | Best Online Clock",
  description: "Free online stopwatch with precision timing, lap recording, and data export. Perfect for sports, workouts, timing activities, and measurements. Multiple time formats available.",
  keywords: "online stopwatch, precision timer, lap timer, sports timer, workout stopwatch, timing tool, free stopwatch, web stopwatch",
  openGraph: {
    title: "Online Stopwatch - Precision Timer with Lap Times | Best Online Clock",
    description: "Free online stopwatch with precision timing, lap recording, and data export. Perfect for sports, workouts, timing activities, and measurements.",
    url: 'https://bestonlineclock.com/stopwatch',
    images: [
      {
        url: '/stopwatch-og-image.png',
        width: 1200,
        height: 630,
        alt: 'Online Stopwatch Interface',
      },
    ],
  },
  twitter: {
    title: "Online Stopwatch - Precision Timer with Lap Times | Best Online Clock",
    description: "Free online stopwatch with precision timing, lap recording, and data export. Perfect for sports, workouts, timing activities, and measurements.",
    images: ['/stopwatch-twitter-image.png'],
  },
  alternates: {
    canonical: '/stopwatch',
  },
};

export default function StopwatchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
