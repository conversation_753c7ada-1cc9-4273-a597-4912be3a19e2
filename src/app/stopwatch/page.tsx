"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { StopwatchDisplay } from "@/components/stopwatch/stopwatch-display";
import { StopwatchSettings } from "@/components/stopwatch/stopwatch-settings";
import { StructuredData } from "@/components/seo/structured-data";

// Simple localStorage utilities
const STORAGE_KEY = "stopwatchSettings";

const defaultSettings = {
  timeFormat: "00:00.00",
  textColor: "#000000",
  fontSize: "6rem",
  fontFamily: "monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: "",
};

export default function StopwatchPage() {
  const [settings, setSettings] = useState(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load settings on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error("Error loading settings:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Monitor fullscreen state (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, [isMounted]);

  // Save settings when they change
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.error("Error saving settings:", error);
      }
    }
  }, [settings, isLoaded]);

  // Extract settings for easier access
  const {
    timeFormat,
    textColor,
    fontSize,
    fontFamily,
    position,
    backgroundColor,
    backgroundImage,
  } = settings;

  // Update setting function
  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // Individual setter functions
  const setTimeFormat = (value: string) => updateSetting('timeFormat', value);
  const setTextColor = (value: string) => updateSetting('textColor', value);
  const setFontSize = (value: string) => updateSetting('fontSize', value);
  const setFontFamily = (value: string) => updateSetting('fontFamily', value);
  const setPosition = (value: { x: number; y: number }) => updateSetting('position', value);
  const setBackgroundColor = (value: string) => updateSetting('backgroundColor', value);
  const setBackgroundImage = (value: string) => updateSetting('backgroundImage', value);

  // Apply background styles to the entire page (only to html element to avoid duplication)
  useEffect(() => {
    if (!isLoaded || typeof document === 'undefined') return;

    const htmlElement = document.documentElement;

    // Apply background styles only to html element
    if (backgroundImage) {
      htmlElement.style.backgroundImage = `url("${backgroundImage}")`;
      htmlElement.style.backgroundColor = '';
    } else if (backgroundColor) {
      htmlElement.style.backgroundColor = backgroundColor;
      htmlElement.style.backgroundImage = '';
    } else {
      htmlElement.style.backgroundColor = '';
      htmlElement.style.backgroundImage = '';
    }

    // Set common background properties
    htmlElement.style.backgroundSize = 'cover';
    htmlElement.style.backgroundPosition = 'center';
    htmlElement.style.backgroundRepeat = 'no-repeat';
    htmlElement.style.backgroundAttachment = 'fixed';

    // No cleanup on unmount to avoid interfering with other pages' backgrounds
  }, [backgroundColor, backgroundImage, isLoaded]);

  // No need for mainStyle since background is handled by html element

  // Don't render content until settings are loaded to prevent flash of default values
  if (!isLoaded) {
    return (
      <MainLayout>
        <div className="w-full flex items-center justify-center">
          <div className="text-lg">Loading...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <StructuredData type="stopwatch" />
      {isFullScreen ? (
        // Full screen layout - settings button in bottom-right corner
        <>
          <div className="flex items-center justify-center min-h-screen">
            <StopwatchDisplay
              timeFormat={timeFormat}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - floating in bottom-right corner for fullscreen */}
          <div className="fixed bottom-4 right-4 z-50 md:top-4 md:bottom-auto">
            <StopwatchSettings
              timeFormat={timeFormat}
              setTimeFormat={setTimeFormat}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>
        </>
      ) : (
        // Non-fullscreen layout - better mobile positioning
        <>
          <div
            className="w-full flex items-center justify-center px-4 py-8 md:py-4"
            style={{
              minHeight: 'calc(100vh - 120px)', // Account for navigation
            }}
          >
            <StopwatchDisplay
              timeFormat={timeFormat}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 z-50 md:top-20 md:bottom-auto md:right-4">
            <StopwatchSettings
              timeFormat={timeFormat}
              setTimeFormat={setTimeFormat}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
            />
          </div>
        </>
      )}
    </MainLayout>
  );
}
