import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Online Timer - Free Countdown Timer | Best Online Clock",
  description: "Free online countdown timer with customizable alerts and sounds. Perfect for cooking, workouts, study sessions, and productivity. Set multiple timers with visual and audio notifications.",
  keywords: "online timer, countdown timer, cooking timer, workout timer, study timer, productivity timer, free timer, web timer",
  openGraph: {
    title: "Online Timer - Free Countdown Timer | Best Online Clock",
    description: "Free online countdown timer with customizable alerts and sounds. Perfect for cooking, workouts, study sessions, and productivity.",
    url: 'https://bestonlineclock.com/timer',
    images: [
      {
        url: '/timer-og-image.png',
        width: 1200,
        height: 630,
        alt: 'Online Timer Interface',
      },
    ],
  },
  twitter: {
    title: "Online Timer - Free Countdown Timer | Best Online Clock",
    description: "Free online countdown timer with customizable alerts and sounds. Perfect for cooking, workouts, study sessions, and productivity.",
    images: ['/timer-twitter-image.png'],
  },
  alternates: {
    canonical: '/timer',
  },
};

export default function TimerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
