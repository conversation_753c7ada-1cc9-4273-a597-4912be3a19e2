"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { TimerDisplay } from "@/components/timer/timer-display";
import { TimerSettings } from "@/components/timer/timer-settings";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { StructuredData } from "@/components/seo/structured-data";

const TIMER_STORAGE_KEY = "timer-settings";

// Default timer settings
const defaultTimerSettings = {
  sound: "default",
  backgroundColor: "#ffffff",
  backgroundImage: "",
  position: { x: 0, y: 0 },
  timerDuration: 0,
  originalTimerDuration: 0, // Store the original timer duration for reset
  timerStartTime: 0,
  isTimerRunning: false,
};

export default function TimerPage() {
  const [isTimerComplete, setIsTimerComplete] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [settings, setSettings] = useState(defaultTimerSettings);

  // Destructure settings for easier access
  const { sound, backgroundColor, backgroundImage, position, timerDuration, originalTimerDuration, timerStartTime, isTimerRunning } = settings;

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Load settings on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(TIMER_STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({ ...defaultTimerSettings, ...parsed });
      }
    } catch (error) {
      console.error("Error loading timer settings:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save settings when they change
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.error("Error saving timer settings:", error);
      }
    }
  }, [settings, isLoaded]);

  // Monitor fullscreen state (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, [isMounted]);

  // Apply background styles to the entire page
  useEffect(() => {
    if (!isLoaded || typeof document === 'undefined') return;

    const htmlElement = document.documentElement;
    const bodyElement = document.body;

    // Apply background styles
    if (backgroundImage) {
      htmlElement.style.backgroundImage = `url("${backgroundImage}")`;
      bodyElement.style.backgroundImage = `url("${backgroundImage}")`;
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    } else if (backgroundColor) {
      htmlElement.style.backgroundColor = backgroundColor;
      bodyElement.style.backgroundColor = backgroundColor;
      htmlElement.style.backgroundImage = 'none';
      bodyElement.style.backgroundImage = 'none';
    } else {
      htmlElement.style.backgroundImage = 'none';
      bodyElement.style.backgroundImage = 'none';
      htmlElement.style.backgroundColor = '';
      bodyElement.style.backgroundColor = '';
    }

    // Common background properties
    [htmlElement, bodyElement].forEach(el => {
      el.style.backgroundSize = 'cover';
      el.style.backgroundPosition = 'center';
      el.style.backgroundRepeat = 'no-repeat';
      el.style.backgroundAttachment = 'fixed';
    });

    return () => {
      // Cleanup on unmount
      [htmlElement, bodyElement].forEach(el => {
        el.style.backgroundImage = '';
        el.style.backgroundColor = '';
      });
    };
  }, [backgroundColor, backgroundImage, isLoaded]);

  // Update setting function
  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // Individual setter functions
  const setSound = (value: string) => updateSetting('sound', value);
  const setBackgroundColor = (value: string) => updateSetting('backgroundColor', value);
  const setBackgroundImage = (value: string) => updateSetting('backgroundImage', value);
  const setPosition = (value: { x: number; y: number }) => updateSetting('position', value);
  const setTimerDuration = (value: number) => updateSetting('timerDuration', value);
  const setOriginalTimerDuration = (value: number) => updateSetting('originalTimerDuration', value);
  const setTimerStartTime = (value: number) => updateSetting('timerStartTime', value);
  const setIsTimerRunning = (value: boolean) => updateSetting('isTimerRunning', value);

  const handleSetTimer = (duration: number, timerSound?: string) => {
    setTimerDuration(duration);
    setOriginalTimerDuration(duration); // Store the original duration for reset
    setTimerStartTime(0); // Don't set start time until user clicks start
    setIsTimerRunning(false); // Don't auto-start the timer
    setIsTimerComplete(false);
    if (timerSound) {
      setSound(timerSound);
    }
  };

  const handleTimerComplete = () => {
    setIsTimerComplete(true);
    // Stop the timer when it completes
    setIsTimerRunning(false);
  };

  const handleCloseDialog = () => {
    setIsTimerComplete(false);
    // Clear the timer completely when closing the dialog
    setTimerDuration(0);
    setTimerStartTime(0);
    setIsTimerRunning(false);
  };

  const handleTimerStart = () => {
    setTimerStartTime(Date.now());
    setIsTimerRunning(true);
  };

  const handleTimerPause = () => {
    setIsTimerRunning(false);
    // When pausing, we need to calculate the remaining time and update the duration
    if (timerStartTime > 0) {
      const elapsed = Date.now() - timerStartTime;
      const remaining = Math.max(0, timerDuration - elapsed);
      setTimerDuration(remaining);
      setTimerStartTime(0); // Reset start time so next start is fresh
    }
  };

  const handleTimerReset = () => {
    setIsTimerRunning(false);
    setTimerStartTime(0);
    // Reset to the original timer duration
    setTimerDuration(originalTimerDuration);
  };

  // Create inline style for the background
  const mainStyle = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative' as 'relative',
    backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
    backgroundColor: backgroundColor || 'transparent',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: 'fixed',
  };

  // Don't render content until settings are loaded to prevent flash of default values
  if (!isLoaded) {
    return (
      <MainLayout>
        <div className="w-full flex items-center justify-center">
          <div className="text-lg">Loading...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <StructuredData type="timer" />
      {isFullScreen ? (
        // Full screen layout - settings button in top-right corner
        <>
          <div
            className="w-full h-screen flex items-center justify-center"
            style={{
              ...mainStyle,
              minHeight: '100vh', // Full viewport height
            }}
          >
            {timerDuration > 0 ? (
              <TimerDisplay
                initialTime={timerDuration}
                originalTime={originalTimerDuration}
                onComplete={handleTimerComplete}
                sound={sound}
                position={position}
                startTime={timerStartTime}
                isRunning={isTimerRunning}
                onStart={handleTimerStart}
                onPause={handleTimerPause}
                onReset={handleTimerReset}
              />
            ) : (
              <div
                className="flex flex-col items-center justify-center w-full h-full relative"
                style={{
                  transform: `translate(${position.x}px, ${position.y}px)`,
                  transition: 'transform 0.3s ease'
                }}
              >
                <div className="text-center text-gray-600 dark:text-gray-300 p-12">
                  <div className="text-8xl mb-6">⏱️</div>
                  <div className="text-3xl font-bold mb-4">Timer</div>
                  <div className="text-xl mb-2">Set a timer to get started</div>
                  <div className="text-lg opacity-75">Use the settings panel to configure your timer</div>
                </div>
              </div>
            )}
          </div>

          {/* Settings panel - floating in top-right corner for fullscreen */}
          <div className="fixed top-4 right-4 z-50">
            <TimerSettings
              onSetTimer={handleSetTimer}
              sound={sound}
              setSound={setSound}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
              position={position}
              setPosition={setPosition}
            />
          </div>
        </>
      ) : (
        // Non-fullscreen layout - full width timer with floating settings
        <>
          <div
            className="w-full flex items-center justify-center px-4 py-8 md:py-4"
            style={{
              ...mainStyle,
              minHeight: 'calc(100vh - 120px)', // Account for navigation
            }}
          >
            {timerDuration > 0 ? (
              <TimerDisplay
                initialTime={timerDuration}
                originalTime={originalTimerDuration}
                onComplete={handleTimerComplete}
                sound={sound}
                position={position}
                startTime={timerStartTime}
                isRunning={isTimerRunning}
                onStart={handleTimerStart}
                onPause={handleTimerPause}
                onReset={handleTimerReset}
              />
            ) : (
              <div
                className="flex flex-col items-center justify-center w-full h-full relative"
                style={{
                  transform: `translate(${position.x}px, ${position.y}px)`,
                  transition: 'transform 0.3s ease'
                }}
              >
                <div className="text-center text-gray-600 dark:text-gray-300 p-12">
                  <div className="text-8xl mb-6">⏱️</div>
                  <div className="text-3xl font-bold mb-4">Timer</div>
                  <div className="text-xl mb-2">Set a timer to get started</div>
                  <div className="text-lg opacity-75">Use the settings panel to configure your timer →</div>
                </div>
              </div>
            )}
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 z-50 md:top-20 md:bottom-auto md:right-4">
            <TimerSettings
              onSetTimer={handleSetTimer}
              sound={sound}
              setSound={setSound}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
              position={position}
              setPosition={setPosition}
            />
          </div>
        </>
      )}

      {/* Completion Dialog */}
      <Dialog open={isTimerComplete} onOpenChange={handleCloseDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl">🎉 Timer Complete!</DialogTitle>
          </DialogHeader>
          <div className="text-center py-6">
            <div className="text-6xl mb-4">⏰</div>
            <p className="text-lg mb-6">Your timer has finished!</p>
            <Button
              onClick={handleCloseDialog}
              className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-xl shadow-lg"
            >
              OK
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
