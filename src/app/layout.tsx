import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Best Online Clock - Free Digital Clock, Timer, Alarm & Stopwatch",
  description: "The best free online clock with digital display, customizable timer, alarm clock, and stopwatch. Perfect for work, study, and daily time management. Mobile-friendly and fullscreen capable.",
  keywords: "online clock, digital clock, timer, alarm clock, stopwatch, time management, free clock, web clock, countdown timer",
  authors: [{ name: "Best Online Clock" }],
  creator: "Best Online Clock",
  publisher: "Best Online Clock",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://bestonlineclock.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Best Online Clock - Free Digital Clock, Timer, Alarm & Stopwatch",
    description: "The best free online clock with digital display, customizable timer, alarm clock, and stopwatch. Perfect for work, study, and daily time management.",
    url: 'https://bestonlineclock.com',
    siteName: 'Best Online Clock',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Best Online Clock - Digital Clock Interface',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Best Online Clock - Free Digital Clock, Timer, Alarm & Stopwatch",
    description: "The best free online clock with digital display, customizable timer, alarm clock, and stopwatch. Perfect for work, study, and daily time management.",
    images: ['/twitter-image.png'],
    creator: '@bestonlineclock',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
