"use client";

import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Settings, X } from "lucide-react";

interface TimerSettingsProps {
  onSetTimer: (time: number, sound: string) => void;
  sound: string;
  setSound: (sound: string) => void;
  backgroundColor: string;
  setBackgroundColor: (color: string) => void;
  backgroundImage: string;
  setBackgroundImage: (image: string) => void;
  position: { x: number; y: number };
  setPosition: (position: { x: number; y: number }) => void;
}

export function TimerSettings({
  onSetTimer,
  sound,
  setSound,
  backgroundColor,
  setBackgroundColor,
  backgroundImage,
  setBackgroundImage,
  position,
  setPosition
}: TimerSettingsProps) {
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Set mounted state and detect mobile
  useEffect(() => {
    setIsMounted(true);

    const updateMobileState = () => {
      setIsMobile(window.innerWidth < 768);
    };

    updateMobileState();
    window.addEventListener('resize', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
    };
  }, []);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      if (document.fullscreenElement) {
        setIsCollapsed(true);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, [isMounted]);

  const soundOptions = [
    { value: "default", label: "Default" },
    { value: "bell", label: "Bell" },
    { value: "digital", label: "Digital" },
    { value: "gentle", label: "Gentle" },
    { value: "chime", label: "Chime" },
  ];

  const presetTimes = [
    { label: "1 min", time: 60 * 1000 },
    { label: "5 min", time: 5 * 60 * 1000 },
    { label: "10 min", time: 10 * 60 * 1000 },
    { label: "15 min", time: 15 * 60 * 1000 },
    { label: "30 min", time: 30 * 60 * 1000 },
    { label: "1 hour", time: 60 * 60 * 1000 },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000;
    if (totalMilliseconds > 0) {
      onSetTimer(totalMilliseconds, sound);
    }
  };

  const handlePresetClick = (time: number) => {
    onSetTimer(time, sound);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const moveTimer = (direction: 'up' | 'down' | 'left' | 'right', e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    const step = 20;
    const currentPos = position || { x: 0, y: 0 };
    let newPos;
    switch (direction) {
      case 'up': newPos = { ...currentPos, y: currentPos.y - step }; break;
      case 'down': newPos = { ...currentPos, y: currentPos.y + step }; break;
      case 'left': newPos = { ...currentPos, x: currentPos.x - step }; break;
      case 'right': newPos = { ...currentPos, x: currentPos.x + step }; break;
      default: newPos = currentPos;
    }
    setPosition(newPos);
  };

  return (
    <>
      {/* Collapsed state - Settings button */}
      {isCollapsed && (
        <div
          className="w-12 h-12 bg-white dark:bg-gray-900 rounded-lg shadow-md"
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            zIndex: 50,
          }}
        >
          <button
            onClick={toggleCollapse}
            className="w-full h-full flex items-center justify-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <Settings size={24} />
          </button>
        </div>
      )}

      {/* Expanded state - Settings panel */}
      {!isCollapsed && (
        <div
          className={`bg-white dark:bg-gray-900 shadow-md flex flex-col ${
            isMobile
              ? 'w-screen h-[33vh] rounded-t-xl'
              : 'w-full max-w-md h-[90vh] rounded-lg'
          }`}
          style={{
            position: 'fixed',
            zIndex: 50,
            ...(isMobile ? {
              bottom: '0px',
              left: '0px',
              right: '0px',
            } : {
              bottom: '20px',
              right: '20px',
            }),
          }}
        >
          {/* Header */}
          <div className={`flex justify-between items-center border-b border-gray-200 dark:border-gray-700 flex-shrink-0 ${
            isMobile ? 'p-3' : 'p-4'
          }`}>
            <h2 className={`font-bold ${isMobile ? 'text-base' : 'text-lg md:text-xl'}`}>
              Timer Settings
            </h2>
            <button
              onClick={toggleCollapse}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X size={isMobile ? 18 : 20} />
            </button>
          </div>

          {/* Scrollable Content */}
          <div className={`flex-1 overflow-y-auto ${isMobile ? 'px-3 py-2' : 'px-4 py-2'}`}>
            <div className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>

              {/* Custom Timer Form */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Custom Timer</h3>
                <form onSubmit={handleSubmit} className={`${isMobile ? 'space-y-2' : 'space-y-4'}`}>
                  <div className={`grid grid-cols-3 ${isMobile ? 'gap-2' : 'gap-4'}`}>
                    <div>
                      <label htmlFor="hours" className={`block font-medium mb-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        Hours
                      </label>
                      <input
                        type="number"
                        id="hours"
                        min="0"
                        max="23"
                        value={hours}
                        onChange={(e) => setHours(parseInt(e.target.value) || 0)}
                        className={`w-full border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800 ${isMobile ? 'p-1 text-sm' : 'p-2'}`}
                      />
                    </div>

                    <div>
                      <label htmlFor="minutes" className={`block font-medium mb-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        Minutes
                      </label>
                      <input
                        type="number"
                        id="minutes"
                        min="0"
                        max="59"
                        value={minutes}
                        onChange={(e) => setMinutes(parseInt(e.target.value) || 0)}
                        className={`w-full border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800 ${isMobile ? 'p-1 text-sm' : 'p-2'}`}
                      />
                    </div>

                    <div>
                      <label htmlFor="seconds" className={`block font-medium mb-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        Seconds
                      </label>
                      <input
                        type="number"
                        id="seconds"
                        min="0"
                        max="59"
                        value={seconds}
                        onChange={(e) => setSeconds(parseInt(e.target.value) || 0)}
                        className={`w-full border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800 ${isMobile ? 'p-1 text-sm' : 'p-2'}`}
                      />
                    </div>
                  </div>

                  <Button type="submit" className={`w-full ${isMobile ? 'py-1 text-sm' : ''}`}>
                    Set Timer
                  </Button>
                </form>
              </div>

              {/* Sound Selection */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Completion Sound</h3>
                <Select value={sound} onValueChange={setSound}>
                  <SelectTrigger className={isMobile ? 'h-9' : ''}>
                    <SelectValue placeholder="Select sound" />
                  </SelectTrigger>
                  <SelectContent>
                    {soundOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Preset Times */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Quick Presets</h3>
                <div className={`grid grid-cols-2 ${isMobile ? 'gap-1' : 'gap-2'}`}>
                  {presetTimes.map((preset) => (
                    <Button
                      key={preset.label}
                      variant="outline"
                      onClick={() => handlePresetClick(preset.time)}
                      className={`w-full ${isMobile ? 'py-1 text-xs' : ''}`}
                    >
                      {preset.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Background Settings */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Background</h3>
                <div className={`${isMobile ? 'space-y-2' : 'space-y-3'}`}>
                  <div>
                    <label htmlFor="backgroundColor" className={`block mb-1 ${isMobile ? 'text-sm' : ''}`}>Background Color</label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        id="backgroundColor"
                        value={backgroundColor || "#ffffff"}
                        onChange={(e) => {
                          setBackgroundColor(e.target.value);
                          setBackgroundImage(''); // Clear background image when setting color
                        }}
                        className={`w-full rounded ${isMobile ? 'h-8' : 'h-10'} border border-gray-300 dark:border-gray-700`}
                      />
                      <button
                        onClick={() => setBackgroundColor('')}
                        className={`px-2 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded ${isMobile ? 'text-xs' : 'text-sm'}`}
                      >
                        Clear
                      </button>
                    </div>
                  </div>

                  {!isMobile && (
                    <>
                      <div>
                        <label htmlFor="backgroundImageUrl" className="block mb-1">Background Image URL</label>
                        <div className="flex gap-2">
                          <input
                            type="url"
                            id="backgroundImageUrl"
                            value={backgroundImage}
                            onChange={(e) => {
                              setBackgroundImage(e.target.value);
                              setBackgroundColor(''); // Clear background color when setting image
                            }}
                            placeholder="https://example.com/image.jpg"
                            className="flex-1 p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
                          />
                          <button
                            onClick={() => {
                              if (backgroundImage) {
                                // Test if the image URL is valid
                                const img = new Image();
                                img.onload = () => {
                                  alert("Image loaded successfully!");
                                };
                                img.onerror = () => {
                                  alert("Failed to load image. Please check the URL.");
                                };
                                img.src = backgroundImage;
                              }
                            }}
                            className="px-2 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                          >
                            Test
                          </button>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="backgroundImageUpload" className="block mb-1">Or Upload Local Image</label>
                        <input
                          type="file"
                          id="backgroundImageUpload"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Convert to base64 for persistent storage
                              const reader = new FileReader();
                              reader.onload = (event) => {
                                const base64String = event.target?.result as string;
                                setBackgroundImage(base64String);
                                setBackgroundColor(''); // Clear background color when setting image
                              };
                              reader.readAsDataURL(file);
                            }
                          }}
                          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
                        />
                      </div>

                      <div className="flex justify-between mt-2">
                        <button
                          onClick={() => {
                            setBackgroundImage('');
                            setBackgroundColor('');
                          }}
                          className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-200 rounded text-sm"
                        >
                          Clear All
                        </button>

                        <button
                          onClick={() => {
                            // Apply a sample background image
                            const sampleImage = "https://source.unsplash.com/random/1920x1080/?nature";
                            setBackgroundImage(sampleImage);
                            setBackgroundColor('');
                          }}
                          className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-200 rounded text-sm"
                        >
                          Sample Image
                        </button>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Position Controls - Desktop only */}
              {!isMobile && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Timer Position</h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <div></div>
                      <Button
                        variant="outline"
                        onClick={(e) => moveTimer('up', e)}
                        className="w-full"
                      >
                        ↑
                      </Button>
                      <div></div>

                      <Button
                        variant="outline"
                        onClick={(e) => moveTimer('left', e)}
                        className="w-full"
                      >
                        ←
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setPosition({ x: 0, y: 0 })}
                        className="w-full text-xs"
                      >
                        Center
                      </Button>
                      <Button
                        variant="outline"
                        onClick={(e) => moveTimer('right', e)}
                        className="w-full"
                      >
                        →
                      </Button>

                      <div></div>
                      <Button
                        variant="outline"
                        onClick={(e) => moveTimer('down', e)}
                        className="w-full"
                      >
                        ↓
                      </Button>
                      <div></div>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                      Position: ({position?.x || 0}, {position?.y || 0})
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>

          {/* Bottom OK Button */}
          <div className={`border-t border-gray-200 dark:border-gray-700 flex-shrink-0 ${isMobile ? 'p-3' : 'p-4'}`}>
            <button
              onClick={toggleCollapse}
              className={`w-full px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium ${isMobile ? 'py-2 text-sm' : 'py-2'}`}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </>
  );
}
