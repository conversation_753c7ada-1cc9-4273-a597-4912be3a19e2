interface StructuredDataProps {
  type?: 'homepage' | 'timer' | 'alarm' | 'stopwatch';
}

export function StructuredData({ type = 'homepage' }: StructuredDataProps) {
  const baseUrl = 'https://bestonlineclock.com';

  const webApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Best Online Clock",
    "description": "Free online clock with digital display, timer, alarm, and stopwatch features. Perfect for time management and productivity.",
    "url": baseUrl,
    "applicationCategory": "UtilitiesApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Digital Clock Display",
      "Customizable Timer",
      "Alarm Clock",
      "Stopwatch",
      "Fullscreen Mode",
      "Mobile Responsive",
      "Custom Backgrounds",
      "Multiple Time Formats"
    ],
    "screenshot": `${baseUrl}/screenshot.png`,
    "softwareVersion": "1.0",
    "author": {
      "@type": "Organization",
      "name": "Best Online Clock"
    }
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Best Online Clock",
    "url": baseUrl,
    "description": "Providing the best free online clock tools for time management and productivity.",
    "sameAs": [
      "https://twitter.com/bestonlineclock",
      "https://facebook.com/bestonlineclock"
    ]
  };

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": baseUrl
      },
      ...(type !== 'homepage' ? [{
        "@type": "ListItem",
        "position": 2,
        "name": type.charAt(0).toUpperCase() + type.slice(1),
        "item": `${baseUrl}/${type}`
      }] : [])
    ]
  };

  const faqSchema = type === 'homepage' ? {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Is this online clock free to use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our online clock is completely free to use. You can access all features including the digital clock, timer, alarm, and stopwatch without any cost or registration."
        }
      },
      {
        "@type": "Question",
        "name": "Can I use this clock in fullscreen mode?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, you can use our clock in fullscreen mode for a distraction-free experience. Simply click the fullscreen button to expand the clock to fill your entire screen."
        }
      },
      {
        "@type": "Question",
        "name": "Does the online clock work on mobile devices?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our online clock is fully responsive and works perfectly on mobile devices, tablets, and desktop computers. The interface adapts to your screen size for optimal usability."
        }
      },
      {
        "@type": "Question",
        "name": "Can I customize the clock appearance?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, you can customize various aspects of the clock including colors, fonts, background images, time format (12/24 hour), and display options like showing seconds, date, and weekday."
        }
      },
      {
        "@type": "Question",
        "name": "Do I need to install anything to use this clock?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "No installation required! Our online clock runs directly in your web browser. Simply visit our website and start using the clock, timer, alarm, and stopwatch features immediately."
        }
      }
    ]
  } : null;

  const schemas = [webApplicationSchema, organizationSchema, breadcrumbSchema];
  if (faqSchema) schemas.push(faqSchema);

  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema, null, 2)
          }}
        />
      ))}
    </>
  );
}
