"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { formatTimeWithMilliseconds } from "@/lib/utils";
import { Download, Trash2 } from "lucide-react";

interface Lap {
  id: number;
  time: number;
  total: number;
}

interface StopwatchDisplayProps {
  timeFormat?: string;
  textColor?: string;
  fontSize?: string;
  fontFamily?: string;
  position?: { x: number; y: number };
}

export function StopwatchDisplay({
  timeFormat = "00:00.00",
  textColor = "#000000",
  fontSize = "6rem",
  fontFamily = "monospace",
  position = { x: 0, y: 0 },
}: StopwatchDisplayProps) {
  const [time, setTime] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [laps, setLaps] = useState<Lap[]>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const lastLapTimeRef = useRef<number>(0);

  useEffect(() => {
    if (isRunning) {
      startTimeRef.current = Date.now() - time;
      intervalRef.current = setInterval(() => {
        setTime(Date.now() - startTimeRef.current);
      }, 10);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning]);

  const handleStart = () => {
    setIsRunning(true);
  };

  const handlePause = () => {
    setIsRunning(false);
  };

  const handleReset = () => {
    setIsRunning(false);
    setTime(0);
    setLaps([]);
    lastLapTimeRef.current = 0;
  };

  const handleLap = () => {
    const lapTime = time - lastLapTimeRef.current;
    lastLapTimeRef.current = time;

    setLaps((prevLaps) => [
      ...prevLaps,
      {
        id: prevLaps.length + 1,
        time: lapTime,
        total: time,
      },
    ]);
  };

  const handleClearLaps = () => {
    setLaps([]);
    lastLapTimeRef.current = 0;
  };

  const handleExportCSV = () => {
    if (laps.length === 0) {
      alert("No lap data to export!");
      return;
    }

    // Create CSV content
    const headers = ["Lap", "Lap Time", "Total Time"];
    const csvContent = [
      headers.join(","),
      ...laps.map(lap => [
        lap.id,
        `"${formatTimeWithMilliseconds(lap.time, timeFormat)}"`,
        `"${formatTimeWithMilliseconds(lap.total, timeFormat)}"`
      ].join(","))
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `stopwatch-laps-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div
      className="flex flex-col items-center w-full h-full relative"
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
        transition: 'transform 0.3s ease',
      }}
    >
      <div
        className="mb-8"
        style={{
          fontSize: fontSize,
          fontFamily: fontFamily,
          color: textColor,
        }}
      >
        {formatTimeWithMilliseconds(time, timeFormat)}
      </div>

      <div className="flex flex-col items-center space-y-6 mb-8">
        {/* Primary Action Area */}
        <div className="flex items-center space-x-6">
          {!isRunning ? (
            <>
              {/* Start/Resume Button - Large and prominent */}
              <Button
                onClick={handleStart}
                className="px-12 py-4 text-xl font-semibold bg-green-600 hover:bg-green-700 text-white rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105"
              >
                {time > 0 ? 'Resume' : 'Start'}
              </Button>

              {/* Reset Button - Secondary, only show if there's data */}
              {(time > 0 || laps.length > 0) && (
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="px-6 py-3 text-lg border-2 border-gray-400 hover:border-gray-600 rounded-xl transition-all duration-200"
                >
                  Reset
                </Button>
              )}
            </>
          ) : (
            <>
              {/* Lap Button - Extra Large when running for frequent use */}
              <Button
                onClick={handleLap}
                className="px-16 py-6 text-2xl font-bold bg-blue-600 hover:bg-blue-700 text-white rounded-2xl shadow-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                Lap
              </Button>

              {/* Pause Button - Smaller, positioned to the side */}
              <Button
                variant="outline"
                onClick={handlePause}
                className="px-6 py-3 text-lg border-2 border-orange-400 hover:border-orange-600 text-orange-600 hover:text-orange-700 rounded-xl transition-all duration-200"
              >
                Pause
              </Button>
            </>
          )}
        </div>
      </div>

      {laps.length > 0 && (
        <div className="w-full max-w-md">
          <h3 className="text-lg font-medium mb-2" style={{ color: textColor }}>Laps</h3>
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-100 dark:bg-gray-800">
                <tr>
                  <th className="py-2 px-4 text-left" style={{ color: textColor }}>Lap</th>
                  <th className="py-2 px-4 text-left" style={{ color: textColor }}>Time</th>
                  <th className="py-2 px-4 text-left" style={{ color: textColor }}>Total</th>
                </tr>
              </thead>
              <tbody>
                {laps.map((lap) => (
                  <tr key={lap.id} className="border-t">
                    <td className="py-2 px-4" style={{ color: textColor }}>{lap.id}</td>
                    <td className="py-2 px-4" style={{ color: textColor }}>{formatTimeWithMilliseconds(lap.time, timeFormat)}</td>
                    <td className="py-2 px-4" style={{ color: textColor }}>{formatTimeWithMilliseconds(lap.total, timeFormat)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Export and Clear buttons */}
          <div className="flex space-x-2 mt-4">
            <Button
              variant="outline"
              onClick={handleExportCSV}
              className="flex items-center space-x-2"
            >
              <Download size={16} />
              <span>Export CSV</span>
            </Button>
            <Button
              variant="outline"
              onClick={handleClearLaps}
              className="flex items-center space-x-2 text-red-600 hover:text-red-700"
            >
              <Trash2 size={16} />
              <span>Clear Data</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
