"use client";

import { useState, useEffect } from "react";
import { formatTime, getShortWeekday, getShortMonth, getWeekNumber } from "@/lib/utils";

interface DigitalClockProps {
  showSeconds?: boolean;
  showWeekday?: boolean;
  showDate?: boolean;
  showWeekNumber?: boolean;
  use12Hours?: boolean;
  textColor?: string;
  fontSize?: string;
  fontFamily?: string;
  position?: { x: number; y: number };
  children?: React.ReactNode;
}

export function DigitalClock({
  showSeconds = true,
  showWeekday = true,
  showDate = true,
  showWeekNumber = true,
  use12Hours = false,
  textColor = "#000000",
  fontSize = "6rem",
  fontFamily = "monospace",
  position = { x: 0, y: 0 },
  children,
}: DigitalClockProps) {
  const [time, setTime] = useState<Date | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Set initial time and mounted state
    setTime(new Date());
    setIsMounted(true);

    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  // Don't render anything until mounted to avoid hydration mismatch
  if (!isMounted || !time) {
    return (
      <div
        className="flex flex-col items-center justify-center w-full h-full relative"
        style={{
          transform: `translate(${position.x}px, ${position.y}px)`,
          transition: 'transform 0.3s ease',
        }}
      >
        <div
          className="text-center relative"
          style={{
            color: textColor,
            fontSize,
            fontFamily,
          }}
        >
          --:--{showSeconds ? ':--' : ''}
        </div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col items-center justify-center w-full h-full relative"
      style={{
        transform: `translate(${position.x}px, ${position.y}px)`,
        transition: 'transform 0.3s ease',
      }}
    >
      <div
        className="text-center relative"
        style={{
          color: textColor,
          fontSize,
          fontFamily,
        }}
      >
        {formatTime(time, showSeconds, use12Hours)}
      </div>

      {(showWeekday || showDate || showWeekNumber) && (
        <div
          className="text-center mt-2"
          style={{
            color: textColor,
            fontFamily,
            fontSize: `calc(${fontSize} / 4)`,
          }}
        >
          {(() => {
            const weekday = showWeekday ? getShortWeekday(time) : '';
            const month = showDate ? getShortMonth(time) : '';
            const day = showDate ? time.getDate() : '';
            const year = showDate ? time.getFullYear() : '';
            const weekNumber = showWeekNumber ? getWeekNumber(time) : '';

            let dateString = '';

            if (showWeekday) {
              dateString += weekday;
            }

            if (showDate) {
              if (dateString) dateString += ' - ';
              dateString += `${month} ${day}, ${year}`;
            }

            if (showWeekNumber) {
              if (dateString) dateString += ' - ';
              dateString += `Week ${weekNumber}`;
            }

            return dateString;
          })()}
        </div>
      )}

      {/* Render children (like alarm indicator) */}
      {children}
    </div>
  );
}
