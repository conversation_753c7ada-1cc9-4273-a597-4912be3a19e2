"use client";

import { useState, useEffect } from "react";
import { AlarmForm } from "./alarm-form";
import { Settings, X } from "lucide-react";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface ClockSettings {
  showSeconds: boolean;
  showWeekday: boolean;
  showDate: boolean;
  showWeekNumber: boolean;
  use12Hours: boolean;
  textColor: string;
  fontSize: string;
  fontFamily: string;
  position: { x: number; y: number };
  backgroundColor: string;
  backgroundImage: string;
}

interface AlarmSettingsProps {
  onAddAlarm: (alarm: Alarm) => boolean;
  onTestAlarm: (alarm: Omit<Alarm, 'id' | 'enabled'>) => void;
  existingAlarms: Alarm[];
  clockSettings: ClockSettings;
  setClockSettings: (settings: ClockSettings) => void;
}

export function AlarmSettings({
  onAddAlarm,
  onTestAlarm,
  existingAlarms,
  clockSettings,
  setClockSettings
}: AlarmSettingsProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [fontSizeValue, setFontSizeValue] = useState(() => parseInt(clockSettings.fontSize));

  // Set mounted state and detect mobile
  useEffect(() => {
    setIsMounted(true);

    const updateMobileState = () => {
      setIsMobile(window.innerWidth < 768);
    };

    updateMobileState();
    window.addEventListener('resize', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
    };
  }, []);

  // Update fontSizeValue when fontSize prop changes
  useEffect(() => {
    const newValue = parseInt(clockSettings.fontSize);
    if (newValue !== fontSizeValue) {
      setFontSizeValue(newValue);
    }
  }, [clockSettings.fontSize]);

  // Update fontSize when fontSizeValue changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const newFontSize = `${fontSizeValue}rem`;
      if (newFontSize !== clockSettings.fontSize) {
        setClockSettings({ ...clockSettings, fontSize: newFontSize });
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [fontSizeValue, clockSettings, setClockSettings]);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      if (document.fullscreenElement) {
        setIsCollapsed(true);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, [isMounted]);

  const fontOptions = [
    { value: "monospace", label: "Monospace" },
    { value: "sans-serif", label: "Sans Serif" },
    { value: "serif", label: "Serif" },
    { value: "Arial", label: "Arial" },
    { value: "Verdana", label: "Verdana" },
    { value: "Helvetica", label: "Helvetica" },
    { value: "Times New Roman", label: "Times New Roman" },
    { value: "Courier New", label: "Courier New" },
  ];

  const handleMove = (direction: 'up' | 'down' | 'left' | 'right') => {
    const step = 20;
    const newPosition = { ...clockSettings.position };

    switch (direction) {
      case 'up':
        newPosition.y -= step;
        break;
      case 'down':
        newPosition.y += step;
        break;
      case 'left':
        newPosition.x -= step;
        break;
      case 'right':
        newPosition.x += step;
        break;
    }

    setClockSettings({ ...clockSettings, position: newPosition });
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <>
      {/* Collapsed state - Settings button */}
      {isCollapsed && (
        <div
          className="w-12 h-12 bg-white dark:bg-gray-900 rounded-lg shadow-md"
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            zIndex: 50,
          }}
        >
          <button
            onClick={toggleCollapse}
            className="w-full h-full flex items-center justify-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <Settings size={24} />
          </button>
        </div>
      )}

      {/* Expanded state - Settings panel */}
      {!isCollapsed && (
        <div
          className={`bg-white dark:bg-gray-900 shadow-md flex flex-col ${
            isMobile
              ? 'w-screen h-[33vh] rounded-t-xl'
              : 'w-full max-w-md h-[90vh] rounded-lg'
          }`}
          style={{
            position: 'fixed',
            zIndex: 50,
            ...(isMobile ? {
              bottom: '0px',
              left: '0px',
              right: '0px',
            } : {
              bottom: '20px',
              right: '20px',
            }),
          }}
        >
          {/* Header */}
          <div className={`flex justify-between items-center border-b border-gray-200 dark:border-gray-700 flex-shrink-0 ${
            isMobile ? 'p-3' : 'p-4'
          }`}>
            <h2 className={`font-bold ${isMobile ? 'text-base' : 'text-lg md:text-xl'}`}>
              Alarm Settings
            </h2>
            <button
              onClick={toggleCollapse}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X size={isMobile ? 18 : 20} />
            </button>
          </div>

          {/* Scrollable Content */}
          <div className={`flex-1 overflow-y-auto ${isMobile ? 'px-3 py-2' : 'px-4 py-2'}`}>
            <div className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>

              {/* Add New Alarm */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Add New Alarm</h3>
                <AlarmForm
                  onAddAlarm={onAddAlarm}
                  onTestAlarm={onTestAlarm}
                  existingAlarms={existingAlarms}
                />
              </div>

              {/* Clock Display Options */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Clock Display</h3>
                <div className={`grid grid-cols-2 ${isMobile ? 'gap-2' : 'gap-4'}`}>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showSeconds"
                      checked={clockSettings.showSeconds}
                      onChange={(e) => setClockSettings({ ...clockSettings, showSeconds: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="showSeconds" className={isMobile ? 'text-sm' : ''}>Show Seconds</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="use12Hours"
                      checked={clockSettings.use12Hours}
                      onChange={(e) => setClockSettings({ ...clockSettings, use12Hours: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="use12Hours" className={isMobile ? 'text-sm' : ''}>12 Hours (AM/PM)</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showWeekday"
                      checked={clockSettings.showWeekday}
                      onChange={(e) => setClockSettings({ ...clockSettings, showWeekday: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="showWeekday" className={isMobile ? 'text-sm' : ''}>Show Weekday</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showDate"
                      checked={clockSettings.showDate}
                      onChange={(e) => setClockSettings({ ...clockSettings, showDate: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="showDate" className={isMobile ? 'text-sm' : ''}>Show Date</label>
                  </div>
                  <div className="flex items-center col-span-2">
                    <input
                      type="checkbox"
                      id="showWeekNumber"
                      checked={clockSettings.showWeekNumber}
                      onChange={(e) => setClockSettings({ ...clockSettings, showWeekNumber: e.target.checked })}
                      className="mr-2"
                    />
                    <label htmlFor="showWeekNumber" className={isMobile ? 'text-sm' : ''}>Show Week Number</label>
                  </div>
                </div>
              </div>

              {/* Clock Appearance */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Clock Appearance</h3>
                <div className={`${isMobile ? 'space-y-2' : 'space-y-4'}`}>
                  <div>
                    <label htmlFor="textColor" className={`block mb-1 ${isMobile ? 'text-sm' : ''}`}>Text Color</label>
                    <input
                      type="color"
                      id="textColor"
                      value={clockSettings.textColor}
                      onChange={(e) => setClockSettings({ ...clockSettings, textColor: e.target.value })}
                      className={`w-full rounded ${isMobile ? 'h-8' : 'h-10'}`}
                    />
                  </div>

                  <div>
                    <label className={`block mb-1 ${isMobile ? 'text-sm' : ''}`}>Font Size: {fontSizeValue}rem</label>
                    <Slider
                      value={[fontSizeValue]}
                      min={1}
                      max={12}
                      step={1}
                      onValueChange={(value) => setFontSizeValue(value[0])}
                      className={isMobile ? 'my-2' : 'my-4'}
                    />
                  </div>

                  <div>
                    <label htmlFor="fontFamily" className={`block mb-1 ${isMobile ? 'text-sm' : ''}`}>Font Family</label>
                    <Select
                      value={clockSettings.fontFamily}
                      onValueChange={(value) => setClockSettings({ ...clockSettings, fontFamily: value })}
                    >
                      <SelectTrigger className={isMobile ? 'h-9' : ''}>
                        <SelectValue placeholder="Select font" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Background Settings */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Background</h3>
                <div className={`${isMobile ? 'space-y-2' : 'space-y-3'}`}>
                  <div>
                    <label htmlFor="backgroundColor" className={`block mb-1 ${isMobile ? 'text-sm' : ''}`}>Background Color</label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        id="backgroundColor"
                        value={clockSettings.backgroundColor || "#ffffff"}
                        onChange={(e) => {
                          setClockSettings({
                            ...clockSettings,
                            backgroundColor: e.target.value,
                            backgroundImage: '' // Clear background image when setting color
                          });
                        }}
                        className={`w-full rounded ${isMobile ? 'h-8' : 'h-10'} border border-gray-300 dark:border-gray-700`}
                      />
                      <button
                        onClick={() => setClockSettings({ ...clockSettings, backgroundColor: '' })}
                        className={`px-2 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded ${isMobile ? 'text-xs' : 'text-sm'}`}
                      >
                        Clear
                      </button>
                    </div>
                  </div>

                  {!isMobile && (
                    <>
                      <div>
                        <label htmlFor="backgroundImageUrl" className="block mb-1">Background Image URL</label>
                        <div className="flex gap-2">
                          <input
                            type="url"
                            id="backgroundImageUrl"
                            value={clockSettings.backgroundImage}
                            onChange={(e) => {
                              setClockSettings({
                                ...clockSettings,
                                backgroundImage: e.target.value,
                                backgroundColor: '' // Clear background color when setting image
                              });
                            }}
                            placeholder="https://example.com/image.jpg"
                            className="flex-1 p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
                          />
                          <button
                            onClick={() => {
                              if (clockSettings.backgroundImage) {
                                const img = new Image();
                                img.onload = () => {
                                  alert("Image loaded successfully!");
                                };
                                img.onerror = () => {
                                  alert("Failed to load image. Please check the URL.");
                                };
                                img.src = clockSettings.backgroundImage;
                              }
                            }}
                            className="px-2 py-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-sm"
                          >
                            Test
                          </button>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="backgroundImageUpload" className="block mb-1">Or Upload Local Image</label>
                        <input
                          type="file"
                          id="backgroundImageUpload"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const reader = new FileReader();
                              reader.onload = (event) => {
                                const base64String = event.target?.result as string;
                                setClockSettings({
                                  ...clockSettings,
                                  backgroundImage: base64String,
                                  backgroundColor: '' // Clear background color when setting image
                                });
                              };
                              reader.readAsDataURL(file);
                            }
                          }}
                          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
                        />
                      </div>

                      <div className="flex justify-between mt-2">
                        <button
                          onClick={() => {
                            setClockSettings({
                              ...clockSettings,
                              backgroundImage: '',
                              backgroundColor: ''
                            });
                          }}
                          className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-200 rounded text-sm"
                        >
                          Clear All
                        </button>

                        <button
                          onClick={() => {
                            const sampleImage = "https://source.unsplash.com/random/1920x1080/?nature";
                            setClockSettings({
                              ...clockSettings,
                              backgroundImage: sampleImage,
                              backgroundColor: ''
                            });
                          }}
                          className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-200 rounded text-sm"
                        >
                          Sample Image
                        </button>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Position Controls - Desktop only */}
              {!isMobile && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Clock Position</h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-2">
                      <div></div>
                      <Button
                        variant="outline"
                        onClick={() => handleMove('up')}
                        className="w-full"
                      >
                        ↑
                      </Button>
                      <div></div>

                      <Button
                        variant="outline"
                        onClick={() => handleMove('left')}
                        className="w-full"
                      >
                        ←
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setClockSettings({ ...clockSettings, position: { x: 0, y: 0 } })}
                        className="w-full text-xs"
                      >
                        Center
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleMove('right')}
                        className="w-full"
                      >
                        →
                      </Button>

                      <div></div>
                      <Button
                        variant="outline"
                        onClick={() => handleMove('down')}
                        className="w-full"
                      >
                        ↓
                      </Button>
                      <div></div>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                      Position: ({clockSettings.position?.x || 0}, {clockSettings.position?.y || 0})
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>

          {/* Bottom OK Button */}
          <div className={`border-t border-gray-200 dark:border-gray-700 flex-shrink-0 ${isMobile ? 'p-3' : 'p-4'}`}>
            <button
              onClick={toggleCollapse}
              className={`w-full px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium ${isMobile ? 'py-2 text-sm' : 'py-2'}`}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </>
  );
}
