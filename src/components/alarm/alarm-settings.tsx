"use client";

import { useState, useEffect } from "react";
import { AlarmForm } from "./alarm-form";
import { Setting<PERSON>, <PERSON> } from "lucide-react";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmSettingsProps {
  onAddAlarm: (alarm: Alarm) => boolean;
  onTestAlarm: (alarm: Omit<Alarm, 'id' | 'enabled'>) => void;
  existingAlarms: Alarm[];
}

export function AlarmSettings({
  onAddAlarm,
  onTestAlarm,
  existingAlarms
}: AlarmSettingsProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Set mounted state and detect mobile
  useEffect(() => {
    setIsMounted(true);

    const updateMobileState = () => {
      setIsMobile(window.innerWidth < 768);
    };

    updateMobileState();
    window.addEventListener('resize', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
    };
  }, []);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    const handleFullScreenChange = () => {
      if (document.fullscreenElement) {
        setIsCollapsed(true);
      }
    };

    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
    };
  }, [isMounted]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <>
      {/* Collapsed state - Settings button */}
      {isCollapsed && (
        <div
          className="w-12 h-12 bg-white dark:bg-gray-900 rounded-lg shadow-md"
          style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            zIndex: 50,
          }}
        >
          <button
            onClick={toggleCollapse}
            className="w-full h-full flex items-center justify-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
          >
            <Settings size={24} />
          </button>
        </div>
      )}

      {/* Expanded state - Settings panel */}
      {!isCollapsed && (
        <div
          className={`bg-white dark:bg-gray-900 shadow-md flex flex-col ${
            isMobile
              ? 'w-screen h-[33vh] rounded-t-xl'
              : 'w-full max-w-md h-[90vh] rounded-lg'
          }`}
          style={{
            position: 'fixed',
            zIndex: 50,
            ...(isMobile ? {
              bottom: '0px',
              left: '0px',
              right: '0px',
            } : {
              bottom: '20px',
              right: '20px',
            }),
          }}
        >
          {/* Header */}
          <div className={`flex justify-between items-center border-b border-gray-200 dark:border-gray-700 flex-shrink-0 ${
            isMobile ? 'p-3' : 'p-4'
          }`}>
            <h2 className={`font-bold ${isMobile ? 'text-base' : 'text-lg md:text-xl'}`}>
              Alarm Settings
            </h2>
            <button
              onClick={toggleCollapse}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X size={isMobile ? 18 : 20} />
            </button>
          </div>

          {/* Scrollable Content */}
          <div className={`flex-1 overflow-y-auto ${isMobile ? 'px-3 py-2' : 'px-4 py-2'}`}>
            <div className={`${isMobile ? 'space-y-3' : 'space-y-6'}`}>

              {/* Add New Alarm */}
              <div>
                <h3 className={`font-medium mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Add New Alarm</h3>
                <AlarmForm
                  onAddAlarm={onAddAlarm}
                  onTestAlarm={onTestAlarm}
                  existingAlarms={existingAlarms}
                />
              </div>

              {/* Clock Settings Notice */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="text-blue-600 dark:text-blue-400 mt-0.5">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className={`text-blue-800 dark:text-blue-200 ${isMobile ? 'text-sm' : 'text-base'}`}>
                      <strong>时钟样式设置：</strong>如果要设置时钟样式（颜色、字体、背景等），请到
                      <a href="/" className="underline hover:no-underline ml-1">Time页面</a> 设置。
                      闹钟页面会自动继承Time页面的时钟配置。
                    </p>
                  </div>
                </div>
              </div>

            </div>
          </div>

          {/* Bottom OK Button */}
          <div className={`border-t border-gray-200 dark:border-gray-700 flex-shrink-0 ${isMobile ? 'p-3' : 'p-4'}`}>
            <button
              onClick={toggleCollapse}
              className={`w-full px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium ${isMobile ? 'py-2 text-sm' : 'py-2'}`}
            >
              OK
            </button>
          </div>
        </div>
      )}
    </>
  );
}
