"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Check, AlertCircle } from "lucide-react";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
}

interface AlarmFormProps {
  onAddAlarm: (alarm: Alarm) => boolean; // Return boolean to indicate success/failure
  onTestAlarm: (alarm: Omit<Alarm, 'id' | 'enabled'>) => void;
  existingAlarms: Alarm[];
}

export function AlarmForm({ onAddAlarm, onTestAlarm, existingAlarms }: AlarmFormProps) {
  const [time, setTime] = useState("08:00");
  const [title, setTitle] = useState("Alarm");
  const [sound, setSound] = useState("default");
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Check for duplicate time (only for non-snoozed alarms)
    const isDuplicate = existingAlarms.some(alarm =>
      alarm.time === time && !alarm.title.includes('(Snoozed)')
    );
    if (isDuplicate) {
      setError(`An alarm for ${time} already exists!`);
      return;
    }

    const newAlarm = {
      id: Date.now().toString(),
      time,
      title,
      sound,
      enabled: true,
    };

    const success = onAddAlarm(newAlarm);

    if (success) {
      // Show success state
      setIsSuccess(true);

      // Reset form
      setTime("08:00");
      setTitle("Alarm");
      setSound("default");

      // Reset success state after 2 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 2000);
    }
  };

  const handleTest = () => {
    onTestAlarm({ time, title, sound });
  };

  const soundOptions = [
    { value: "default", label: "Default" },
    { value: "bell", label: "Bell" },
    { value: "digital", label: "Digital" },
    { value: "gentle", label: "Gentle" },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="time" className="block text-sm font-medium mb-1">
          Time
        </label>
        <input
          type="time"
          id="time"
          value={time}
          onChange={(e) => setTime(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          required
        />
      </div>

      <div>
        <label htmlFor="title" className="block text-sm font-medium mb-1">
          Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          required
        />
      </div>

      <div>
        <label htmlFor="sound" className="block text-sm font-medium mb-1">
          Sound
        </label>
        <Select value={sound} onValueChange={setSound}>
          <SelectTrigger id="sound">
            <SelectValue placeholder="Select sound" />
          </SelectTrigger>
          <SelectContent>
            {soundOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Error message */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <AlertCircle size={16} className="text-red-600 dark:text-red-400" />
          <span className="text-sm text-red-600 dark:text-red-400">{error}</span>
        </div>
      )}

      {/* Test button */}
      <Button
        type="button"
        onClick={handleTest}
        variant="outline"
        className="w-full"
      >
        <Play size={16} className="mr-2" />
        Test Alarm
      </Button>

      {/* Add alarm button */}
      <Button
        type="submit"
        className={`w-full transition-all duration-300 ${
          isSuccess
            ? 'bg-green-600 hover:bg-green-700 text-white'
            : ''
        }`}
      >
        {isSuccess ? (
          <>
            <Check size={16} className="mr-2" />
            Alarm Added Successfully!
          </>
        ) : (
          'Add Alarm'
        )}
      </Button>
    </form>
  );
}
