"use client";

import { useState, useEffect } from "react";
import { AlarmClock, Volume2, VolumeX, Trash2 } from "lucide-react";

interface Alarm {
  id: string;
  time: string;
  title: string;
  sound: string;
  enabled: boolean;
  isTemporary?: boolean;
}

interface AlarmIndicatorProps {
  alarms: Alarm[];
  onToggleAlarm: (id: string) => void;
  onDeleteAlarm: (id: string) => void;
}

export function AlarmIndicator({ alarms, onToggleAlarm, onDeleteAlarm }: AlarmIndicatorProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  const enabledAlarmsCount = alarms.filter(alarm => alarm.enabled).length;
  const hasAlarms = alarms.length > 0;

  // Update current time every minute to refresh next alarm calculation
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Find the next alarm (closest to current time)
  const getNextAlarm = (): Alarm | null => {
    const currentTimeMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();

    const enabledAlarms = alarms.filter(alarm => alarm.enabled);
    if (enabledAlarms.length === 0) return null;

    let nextAlarm: Alarm | null = null;
    let minTimeDiff = Infinity;

    enabledAlarms.forEach(alarm => {
      const [hours, minutes] = alarm.time.split(':').map(Number);
      const alarmTime = hours * 60 + minutes;

      // Calculate time difference (considering next day if alarm is earlier than current time)
      let timeDiff = alarmTime - currentTimeMinutes;
      if (timeDiff <= 0) {
        timeDiff += 24 * 60; // Add 24 hours for next day
      }

      if (timeDiff < minTimeDiff) {
        minTimeDiff = timeDiff;
        nextAlarm = alarm;
      }
    });

    return nextAlarm;
  };

  const nextAlarm = getNextAlarm();

  return (
    <div
      className="flex items-center justify-center space-x-4 z-10"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Next Alarm Time Display */}
      {nextAlarm && (
        <div className="flex items-center space-x-2 bg-white/20 dark:bg-gray-900/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20">
          <span className="text-sm text-gray-600 dark:text-gray-400">Next:</span>
          <span className="font-mono font-bold text-lg text-blue-600 dark:text-blue-400">
            {nextAlarm.time}
          </span>
          <span className="text-sm text-gray-600 dark:text-gray-400 max-w-24 truncate">
            {nextAlarm.title}
          </span>
        </div>
      )}

      {/* Alarm Icon */}
      <div className="relative">
        <div className={`
          flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 cursor-pointer transform
          ${hasAlarms
            ? 'bg-blue-500/20 hover:bg-blue-500/30 text-blue-600 dark:text-blue-400 hover:scale-110'
            : 'bg-gray-500/20 hover:bg-gray-500/30 text-gray-500 dark:text-gray-400 hover:scale-105'
          }
          backdrop-blur-sm border border-white/20 shadow-lg hover:shadow-xl
          ${isHovered ? 'scale-110 shadow-xl' : ''}
        `}>
          <AlarmClock size={24} />
        </div>

        {/* Alarm Count Badge */}
        {enabledAlarmsCount > 0 && (
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
            {enabledAlarmsCount}
          </div>
        )}
      </div>

      {/* Alarm List Dropdown */}
      <div className={`
        absolute top-14 left-1/2 transform -translate-x-1/2 w-80 transition-all duration-500 ease-out origin-top
        ${isHovered
          ? 'opacity-100 scale-100 translate-y-0 visible'
          : 'opacity-0 scale-95 -translate-y-4 invisible'
        }
      `}>
        <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-md rounded-lg shadow-xl border border-white/20 p-4">
          {alarms.length === 0 ? (
            <div className="text-center py-6">
              <AlarmClock className="mx-auto mb-2 text-gray-400" size={32} />
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                No alarms set
              </p>
              <p className="text-gray-500 dark:text-gray-500 text-xs mt-1">
                Use the settings panel to add alarms
              </p>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                  Your Alarms
                </h3>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {enabledAlarmsCount} of {alarms.length} enabled
                </span>
              </div>

              <div className="space-y-2 max-h-64 overflow-y-auto">
                {alarms.map((alarm) => (
                  <div
                    key={alarm.id}
                    className={`
                      flex items-center justify-between p-3 rounded-lg transition-all duration-200
                      ${alarm.isTemporary
                        ? 'bg-orange-50/50 dark:bg-orange-900/20 border border-orange-200/50 dark:border-orange-700/50'
                        : alarm.enabled
                        ? 'bg-blue-50/50 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-700/50'
                        : 'bg-gray-50/50 dark:bg-gray-800/20 border border-gray-200/50 dark:border-gray-600/50'
                      }
                    `}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <AlarmClock
                          size={16}
                          className={alarm.enabled ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400'}
                        />
                        <span className={`
                          font-mono font-bold text-sm
                          ${alarm.enabled ? 'text-gray-800 dark:text-gray-200' : 'text-gray-500 dark:text-gray-400'}
                        `}>
                          {alarm.time}
                        </span>
                      </div>

                      <div>
                        <p className={`
                          text-sm font-medium
                          ${alarm.enabled ? 'text-gray-800 dark:text-gray-200' : 'text-gray-500 dark:text-gray-400'}
                        `}>
                          {alarm.title}
                          {alarm.isTemporary && (
                            <span className="ml-2 px-1.5 py-0.5 text-xs bg-orange-200 dark:bg-orange-800 text-orange-800 dark:text-orange-200 rounded">
                              Temp
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {alarm.sound}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onToggleAlarm(alarm.id);
                        }}
                        className={`
                          p-1.5 rounded-full transition-colors
                          ${alarm.enabled
                            ? 'bg-blue-100 hover:bg-blue-200 text-blue-600 dark:bg-blue-900/50 dark:hover:bg-blue-800/50 dark:text-blue-400'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-400 dark:bg-gray-700/50 dark:hover:bg-gray-600/50'
                          }
                        `}
                        title={alarm.enabled ? 'Disable alarm' : 'Enable alarm'}
                      >
                        {alarm.enabled ? <Volume2 size={12} /> : <VolumeX size={12} />}
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteAlarm(alarm.id);
                        }}
                        className="p-1.5 rounded-full bg-red-100 hover:bg-red-200 text-red-600 dark:bg-red-900/50 dark:hover:bg-red-800/50 dark:text-red-400 transition-colors"
                        title="Delete alarm"
                      >
                        <Trash2 size={12} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
