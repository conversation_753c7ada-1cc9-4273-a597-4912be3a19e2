"use client";

import { useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface AlarmNotificationProps {
  isOpen: boolean;
  onClose: () => void;
  onSnooze: (minutes: number) => void;
  alarm: {
    id?: string;
    time?: string;
    title: string;
    sound: string;
  } | null;
}

export function AlarmNotification({
  isOpen,
  onClose,
  onSnooze,
  alarm,
}: AlarmNotificationProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (isOpen && alarm) {
      // Play alarm sound
      if (audioRef.current) {
        audioRef.current.play().catch((error) => {
          console.error("Failed to play alarm sound:", error);
        });
      }
    } else {
      // Stop alarm sound
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    }
  }, [isOpen, alarm]);

  if (!alarm) return null;

  const isTestMode = alarm.id === 'test';

  return (
    <>
      <audio
        ref={audioRef}
        src={`/sounds/${alarm.sound}.mp3`}
        loop
      />

      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-center text-3xl font-bold">
              {alarm.title}
            </DialogTitle>
            {isTestMode && (
              <p className="text-center text-sm text-blue-600 dark:text-blue-400 font-medium">
                Test Mode
              </p>
            )}
          </DialogHeader>

          <div className="text-center py-8">
            <div className="text-7xl font-bold mb-6 font-mono">
              {alarm.time || new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              {isTestMode ? 'Testing alarm sound' : 'Your alarm is ringing'}
            </p>
          </div>

          <div className="flex flex-col gap-3 p-6">
            {isTestMode ? (
              <Button onClick={onClose} className="w-full py-3 text-lg">
                OK
              </Button>
            ) : (
              <>
                {/* First row: 3 Snooze buttons */}
                <div className="grid grid-cols-3 gap-3">
                  <Button
                    variant="outline"
                    onClick={() => onSnooze(1)}
                    className="py-3 px-2 h-auto flex flex-col"
                  >
                    <span className="font-semibold text-sm">Snooze</span>
                    <span className="text-xs text-gray-500">1 min</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => onSnooze(5)}
                    className="py-3 px-2 h-auto flex flex-col"
                  >
                    <span className="font-semibold text-sm">Snooze</span>
                    <span className="text-xs text-gray-500">5 min</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => onSnooze(10)}
                    className="py-3 px-2 h-auto flex flex-col"
                  >
                    <span className="font-semibold text-sm">Snooze</span>
                    <span className="text-xs text-gray-500">10 min</span>
                  </Button>
                </div>

                {/* Second row: Dismiss button */}
                <Button onClick={onClose} className="w-full py-3 text-lg bg-gray-800 hover:bg-gray-900 text-white">
                  Dismiss
                </Button>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
